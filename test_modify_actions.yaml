# FlexProxy测试配置 - modify_request和modify_response功能测试
global:
  enable: true
  dns_cache_ttl: 300
  dns_no_cache: false
  ip_version_priority: "ipv4"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 10

# 定义动作序列
actions:
  # 测试modify_request - 修改请求头和请求体
  test_modify_request:
    sequence:
      - type: "modify_request"
        headers:
          "User-Agent": "FlexProxy-Test/1.0"
          "X-Custom-Header": "test-value"
          "Authorization": "Bearer test-token"
        remove_headers: ["X-Real-IP", "X-Forwarded-For"]
        body: '{"modified": true, "test": "request body modification"}'

  # 测试modify_response - 修改响应头、状态码和响应体
  test_modify_response:
    sequence:
      - type: "modify_response"
        headers:
          "X-Proxy-Modified": "true"
          "Cache-Control": "no-cache, no-store"
          "Content-Type": "application/json"
        remove_headers: ["Server", "X-Powered-By"]
        status_code: 200
        body: '{"success": true, "message": "Response modified by FlexProxy", "data": {"test": true}}'

  # 组合测试 - 先修改请求，再修改响应
  test_combined_modify:
    sequence:
      - type: "modify_request"
        headers:
          "X-Request-ID": "test-12345"
        body: '{"request": "modified"}'
      - type: "log"
        message: "请求已修改"
        level: "info"

# 定义事件
events:
  # 测试modify_request功能
  - name: "test_modify_request_event"
    enable: true
    trigger_type: "url"
    process_stage: "pre_request"
    priority: 1
    conditions:
      - name: "test_url_condition"
        enable: true
        url_patterns:
          patterns: [".*test-modify-request.*"]
          match_type: "regex"
    matches:
      - name: "modify_request_match"
        enable: true
        conditions: ["test_url_condition"]
        actions:
          - type: "modify_request"
            headers:
              "User-Agent": "FlexProxy-Modified/1.0"
              "X-Test-Header": "request-modified"
            remove_headers: ["Accept-Encoding"]
            body: '{"test": "request body modified", "timestamp": "2024-01-01T00:00:00Z"}'

  # 测试modify_response功能
  - name: "test_modify_response_event"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 1
    conditions:
      - name: "success_status_condition"
        enable: true
        status_codes:
          codes: [200, 201, 202]
          match_type: "include"
    matches:
      - name: "modify_response_match"
        enable: true
        conditions: ["success_status_condition"]
        actions:
          - type: "modify_response"
            headers:
              "X-Response-Modified": "true"
              "X-Modification-Time": "2024-01-01T00:00:00Z"
            remove_headers: ["Set-Cookie"]
            status_code: 200
            body: '{"modified": true, "original_response": "replaced", "proxy": "FlexProxy"}'

  # 测试域名匹配的响应修改
  - name: "test_domain_response_modify"
    enable: true
    trigger_type: "domain"
    process_stage: "post_body"
    priority: 2
    conditions:
      - name: "test_domain_condition"
        enable: true
        domain_patterns:
          patterns: ["httpbin.org", "example.com"]
          match_type: "exact"
    matches:
      - name: "domain_response_modify_match"
        enable: true
        conditions: ["test_domain_condition"]
        actions:
          - type: "modify_response"
            headers:
              "X-Domain-Intercepted": "true"
              "X-Original-Domain": "replaced"
            body: '{"intercepted": true, "message": "This response was modified by FlexProxy", "original_domain": "hidden"}'
