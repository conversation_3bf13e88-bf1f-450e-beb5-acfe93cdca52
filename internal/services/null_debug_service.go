// Package services 提供各种服务实现
package services

import (
	"time"
	
	"github.com/mbndr/logo"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// nullDebugService 空调试服务实现，用于禁用调试功能时
type nullDebugService struct {
	logger *logo.Logger
}

// NewNullDebugService 创建空调试服务实例
func NewNullDebugService(log *logo.Logger) interfaces.DebugService {
	if log == nil {
		log = logo.NewSimpleLogger(nil, logo.INFO, "null-debug", false)
	}
	
	return &nullDebugService{
		logger: log,
	}
}

// Start 启动调试服务（空实现）
func (nds *nullDebugService) Start() error {
	nds.logger.Info("空调试服务已启动（无实际功能）")
	return nil
}

// Stop 停止调试服务（空实现）
func (nds *nullDebugService) Stop() error {
	nds.logger.Info("空调试服务已停止")
	return nil
}

// IsEnabled 检查服务是否启用（空实现，总是返回false）
func (nds *nullDebugService) IsEnabled() bool {
	return false
}

// SetBreakpoint 设置断点（空实现）
func (nds *nullDebugService) SetBreakpoint(id string, condition string) error {
	return nil
}

// RemoveBreakpoint 移除断点（空实现）
func (nds *nullDebugService) RemoveBreakpoint(id string) error {
	return nil
}

// ListBreakpoints 列出断点（空实现，返回空列表）
func (nds *nullDebugService) ListBreakpoints() []interface{} {
	return []interface{}{}
}

// AddWatcher 添加监视器（空实现）
func (nds *nullDebugService) AddWatcher(id string, expression string) error {
	return nil
}

// RemoveWatcher 移除监视器（空实现）
func (nds *nullDebugService) RemoveWatcher(id string) error {
	return nil
}

// GetWatcherValue 获取监视器值（空实现，返回nil）
func (nds *nullDebugService) GetWatcherValue(id string) interface{} {
	return nil
}

// DumpRequest 转储请求（空实现）
func (nds *nullDebugService) DumpRequest(req interface{}) error {
	return nil
}

// DumpResponse 转储响应（空实现）
func (nds *nullDebugService) DumpResponse(resp interface{}) error {
	return nil
}

// StartProfiling 开始性能分析（空实现）
func (nds *nullDebugService) StartProfiling(profileType string) error {
	return nil
}

// StopProfiling 停止性能分析（空实现）
func (nds *nullDebugService) StopProfiling() error {
	return nil
}

// GetProfilingData 获取性能分析数据（空实现，返回nil）
func (nds *nullDebugService) GetProfilingData() interface{} {
	return nil
}

// LogDebug 记录调试日志（空实现）
func (nds *nullDebugService) LogDebug(message string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// GetDebugInfo 获取调试信息（空实现，返回空信息）
func (nds *nullDebugService) GetDebugInfo() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"status":  "disabled",
	}
}

// ExecuteCommand 执行调试命令（空实现）
func (nds *nullDebugService) ExecuteCommand(command string, args []string) (interface{}, error) {
	return nil, nil
}

// GetStats 获取统计信息（空实现，返回空统计）
func (nds *nullDebugService) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":     false,
		"breakpoints": 0,
		"watchers":    0,
		"uptime":      time.Duration(0),
	}
}
