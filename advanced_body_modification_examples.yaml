# FlexProxy 高级 Body 修改配置示例
# 支持多种内容类型：JSON, XML, HTML, 纯文本, URL编码, 二进制数据

global:
  enable: true
  dns_cache_ttl: 300

# 定义高级body修改动作序列
actions:
  # 1. JSON格式修改示例
  modify_json_request:
    sequence:
      - type: "modify_request"
        headers:
          "Content-Type": "application/json"
        body_config:
          content: |
            {
              "api_version": "v2",
              "modified_by": "FlexProxy",
              "timestamp": "2024-01-01T00:00:00Z",
              "data": {
                "user_id": 12345,
                "action": "update_profile",
                "parameters": {
                  "name": "Modified User",
                  "email": "<EMAIL>"
                }
              }
            }
          format: "json"
          # content_type 会自动设置为 application/json

  modify_json_response:
    sequence:
      - type: "modify_response"
        status_code: 200
        body_config:
          content: |
            {
              "success": true,
              "message": "Response modified by FlexProxy",
              "data": {
                "items": [
                  {"id": 1, "name": "Item 1", "modified": true},
                  {"id": 2, "name": "Item 2", "modified": true}
                ],
                "total": 2,
                "proxy_info": {
                  "modified": true,
                  "version": "FlexProxy v1.0"
                }
              }
            }
          format: "json"

  # 2. XML格式修改示例
  modify_xml_request:
    sequence:
      - type: "modify_request"
        body_config:
          content: |
            <?xml version="1.0" encoding="UTF-8"?>
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
              <soap:Header>
                <auth:Authentication xmlns:auth="http://example.com/auth">
                  <auth:Token>modified-token-12345</auth:Token>
                  <auth:ModifiedBy>FlexProxy</auth:ModifiedBy>
                </auth:Authentication>
              </soap:Header>
              <soap:Body>
                <req:UpdateRequest xmlns:req="http://example.com/request">
                  <req:UserId>12345</req:UserId>
                  <req:Data>Modified XML content</req:Data>
                </req:UpdateRequest>
              </soap:Body>
            </soap:Envelope>
          format: "xml"
          content_type: "text/xml; charset=utf-8"

  modify_xml_response:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
              <status>success</status>
              <message>XML response modified by FlexProxy</message>
              <data>
                <item id="1">Modified Item 1</item>
                <item id="2">Modified Item 2</item>
              </data>
              <metadata>
                <modified_by>FlexProxy</modified_by>
                <timestamp>2024-01-01T00:00:00Z</timestamp>
              </metadata>
            </response>
          format: "xml"

  # 3. HTML格式修改示例
  modify_html_response:
    sequence:
      - type: "modify_response"
        headers:
          "Cache-Control": "no-cache"
        body_config:
          content: |
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>页面已被FlexProxy修改</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .modified { background-color: #f0f8ff; padding: 20px; border-left: 4px solid #007acc; }
                </style>
            </head>
            <body>
                <div class="modified">
                    <h1>内容已修改</h1>
                    <p>此页面内容已被FlexProxy代理服务器修改。</p>
                    <ul>
                        <li>修改时间: 2024-01-01 00:00:00</li>
                        <li>代理版本: FlexProxy v1.0</li>
                        <li>修改类型: HTML内容替换</li>
                    </ul>
                </div>
            </body>
            </html>
          format: "html"

  # 4. 纯文本格式修改示例
  modify_text_response:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            FlexProxy 代理服务器响应
            ========================
            
            原始内容已被替换为此文本内容。
            
            修改信息:
            - 修改时间: 2024-01-01 00:00:00
            - 代理版本: FlexProxy v1.0
            - 内容类型: 纯文本
            
            这是一个多行文本内容的示例，
            可以包含任意的文本信息。
          format: "text"
          content_type: "text/plain; charset=utf-8"

  # 5. URL编码格式修改示例
  modify_form_request:
    sequence:
      - type: "modify_request"
        body_config:
          content: "username=flexproxy_user&password=modified_password&action=login&remember=true&modified_by=FlexProxy&timestamp=2024-01-01T00%3A00%3A00Z"
          format: "form"
          # content_type 会自动设置为 application/x-www-form-urlencoded

  # 6. 二进制数据修改示例
  modify_binary_response:
    sequence:
      - type: "modify_response"
        headers:
          "Content-Disposition": "attachment; filename=\"modified_file.bin\""
        body_config:
          content: "RmxleFByb3h5IEJpbmFyeSBEYXRhIEV4YW1wbGUhCkhlbGxvIFdvcmxkIGZyb20gRmxleFByb3h5IQ=="
          encoding: "base64"
          content_type: "application/octet-stream"

  # 7. 自动检测Content-Type示例
  modify_auto_detect:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            {
              "auto_detected": true,
              "message": "Content-Type will be automatically detected as JSON",
              "features": [
                "自动格式检测",
                "智能Content-Type设置",
                "向后兼容性"
              ]
            }
          # 不指定format，系统会自动检测为JSON格式

  # 8. 组合修改示例（同时修改headers和body）
  modify_combined_example:
    sequence:
      - type: "modify_response"
        headers:
          "X-Modified-By": "FlexProxy"
          "X-Modification-Time": "2024-01-01T00:00:00Z"
          "Cache-Control": "no-cache, no-store"
        remove_headers: ["Server", "X-Powered-By"]
        status_code: 200
        body_config:
          content: |
            {
              "status": "modified",
              "original_response": "replaced",
              "proxy_info": {
                "name": "FlexProxy",
                "version": "1.0",
                "features": [
                  "高级body修改",
                  "多格式支持",
                  "自动Content-Type检测"
                ]
              },
              "supported_formats": [
                "JSON",
                "XML", 
                "HTML",
                "纯文本",
                "URL编码",
                "二进制数据"
              ]
            }
          format: "json"
          content_type: "application/json; charset=utf-8"

# 定义事件触发规则
events:
  # JSON API请求修改
  - name: "modify_json_api_requests"
    enable: true
    trigger_type: "url"
    process_stage: "pre_request"
    priority: 1
    conditions:
      - name: "json_api_condition"
        enable: true
        url_patterns:
          patterns: [".*/api/.*", ".*/v[0-9]+/.*"]
          match_type: "regex"
    matches:
      - name: "json_api_match"
        enable: true
        conditions: ["json_api_condition"]
        actions:
          - type: "modify_request"
            body_config:
              content: '{"api_modified": true, "proxy": "FlexProxy"}'
              format: "json"

  # HTML页面响应修改
  - name: "modify_html_responses"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 2
    conditions:
      - name: "html_response_condition"
        enable: true
        status_codes:
          codes: [200, 201]
          match_type: "include"
    matches:
      - name: "html_response_match"
        enable: true
        conditions: ["html_response_condition"]
        actions:
          - type: "modify_response"
            body_config:
              content: '<!DOCTYPE html><html><head><title>Modified</title></head><body><h1>Content Modified by FlexProxy</h1></body></html>'
              format: "html"
