# FlexProxy modify_request 和 modify_response 动作使用指南

## 概述

FlexProxy 现在支持 `modify_request` 和 `modify_response` 动作，可以在代理过程中实时修改HTTP请求和响应的内容。

## 功能特性

### modify_request 动作

**支持的修改操作：**
- ✅ 添加/修改HTTP请求头
- ✅ 删除指定的HTTP请求头
- ✅ 替换HTTP请求体内容（支持多种格式）
- ✅ 自动更新Content-Length头部
- ✅ 自动设置Content-Type头部
- ✅ 内容格式验证

**配置参数：**
- `headers`: 要添加或修改的请求头（键值对）
- `remove_headers`: 要删除的请求头名称列表
- `body`: 新的请求体内容（字符串格式，向后兼容）
- `body_config`: 高级请求体配置（支持多种格式）
- `auto_content_type`: 是否自动设置Content-Type（默认true）

### modify_response 动作

**支持的修改操作：**
- ✅ 添加/修改HTTP响应头
- ✅ 删除指定的HTTP响应头
- ✅ 修改HTTP响应状态码
- ✅ 替换HTTP响应体内容（支持多种格式）
- ✅ 自动更新Content-Length头部
- ✅ 自动设置Content-Type头部
- ✅ 内容格式验证

**配置参数：**
- `headers`: 要添加或修改的响应头（键值对）
- `remove_headers`: 要删除的响应头名称列表
- `status_code`: 新的响应状态码（整数）
- `body`: 新的响应体内容（字符串格式，向后兼容）
- `body_config`: 高级响应体配置（支持多种格式）
- `auto_content_type`: 是否自动设置Content-Type（默认true）

## 支持的内容类型

### 1. JSON格式
- **自动Content-Type**: `application/json`
- **格式验证**: 验证JSON语法正确性
- **使用场景**: API请求/响应、配置文件

### 2. XML格式
- **自动Content-Type**: `application/xml`
- **格式验证**: 基本XML结构验证
- **使用场景**: SOAP请求、RSS/Atom feeds、配置文件

### 3. HTML格式
- **自动Content-Type**: `text/html`
- **格式验证**: 基本HTML结构检查
- **使用场景**: 网页内容修改、错误页面定制

### 4. 纯文本格式
- **自动Content-Type**: `text/plain`
- **格式验证**: 无需验证
- **使用场景**: 日志文件、配置文件、简单文本

### 5. URL编码格式
- **自动Content-Type**: `application/x-www-form-urlencoded`
- **格式验证**: 检查键值对格式
- **使用场景**: 表单提交、POST请求参数

### 6. 二进制数据
- **自动Content-Type**: `application/octet-stream`
- **编码支持**: Base64编码/解码
- **使用场景**: 文件上传、图片、音视频数据

## 配置示例

### 基础配置（向后兼容）

```yaml
actions:
  # 简单修改请求示例
  modify_request_simple:
    sequence:
      - type: "modify_request"
        headers:
          "User-Agent": "FlexProxy/1.0"
          "X-Custom-Header": "custom-value"
        remove_headers: ["X-Real-IP", "Accept-Encoding"]
        body: '{"modified": true, "proxy": "FlexProxy"}'

  # 简单修改响应示例
  modify_response_simple:
    sequence:
      - type: "modify_response"
        headers:
          "X-Proxy-Modified": "true"
        remove_headers: ["Server", "X-Powered-By"]
        status_code: 200
        body: '{"success": true, "message": "Modified by FlexProxy"}'
```

### 高级配置（body_config）

```yaml
actions:
  # JSON格式修改
  modify_json_content:
    sequence:
      - type: "modify_request"
        body_config:
          content: |
            {
              "api_version": "v2",
              "modified_by": "FlexProxy",
              "data": {
                "user_id": 12345,
                "action": "update"
              }
            }
          format: "json"
          content_type: "application/json; charset=utf-8"

  # XML格式修改
  modify_xml_content:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
              <status>success</status>
              <message>Modified by FlexProxy</message>
              <data>
                <item id="1">Modified Item</item>
              </data>
            </response>
          format: "xml"

  # HTML格式修改
  modify_html_content:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            <!DOCTYPE html>
            <html>
            <head>
                <title>Modified by FlexProxy</title>
            </head>
            <body>
                <h1>Content Modified</h1>
                <p>This page was modified by FlexProxy.</p>
            </body>
            </html>
          format: "html"

  # 表单数据修改
  modify_form_data:
    sequence:
      - type: "modify_request"
        body_config:
          content: "username=flexproxy&password=secret&action=login"
          format: "form"

  # 二进制数据修改
  modify_binary_data:
    sequence:
      - type: "modify_response"
        body_config:
          content: "SGVsbG8gRmxleFByb3h5IQ=="  # "Hello FlexProxy!" in base64
          encoding: "base64"
          content_type: "application/octet-stream"

  # 自动检测格式
  modify_auto_detect:
    sequence:
      - type: "modify_response"
        body_config:
          content: '{"auto_detected": true, "format": "json"}'
          # format 和 content_type 会自动检测和设置
```

### 事件触发配置

```yaml
events:
  # 基于URL模式修改请求
  - name: "modify_api_requests"
    enable: true
    trigger_type: "url"
    process_stage: "pre_request"
    priority: 1
    conditions:
      - name: "api_url_condition"
        enable: true
        url_patterns:
          patterns: [".*/api/.*"]
          match_type: "regex"
    matches:
      - name: "api_request_modify"
        enable: true
        conditions: ["api_url_condition"]
        actions:
          - type: "modify_request"
            headers:
              "X-API-Request": "true"
              "X-Request-ID": "req-12345"
            body: '{"api_request": true, "modified": true}'

  # 基于状态码修改响应
  - name: "modify_error_responses"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 1
    conditions:
      - name: "error_status_condition"
        enable: true
        status_codes:
          codes: [404, 500, 502, 503]
          match_type: "include"
    matches:
      - name: "error_response_modify"
        enable: true
        conditions: ["error_status_condition"]
        actions:
          - type: "modify_response"
            status_code: 200
            headers:
              "Content-Type": "application/json"
            body: '{"error": false, "message": "Request handled by proxy"}'
```

## 使用场景

### 1. API请求预处理
```yaml
- type: "modify_request"
  headers:
    "Authorization": "Bearer ${API_TOKEN}"
    "Content-Type": "application/json"
  body: '{"preprocessed": true, "original_request": "modified"}'
```

### 2. 响应内容过滤
```yaml
- type: "modify_response"
  headers:
    "X-Content-Filtered": "true"
  body: '{"filtered": true, "original_content": "removed"}'
```

### 3. 错误响应统一处理
```yaml
- type: "modify_response"
  status_code: 200
  headers:
    "Content-Type": "application/json"
  body: '{"success": false, "error": "Service temporarily unavailable"}'
```

### 4. 安全头部注入
```yaml
- type: "modify_response"
  headers:
    "X-Frame-Options": "DENY"
    "X-Content-Type-Options": "nosniff"
    "X-XSS-Protection": "1; mode=block"
```

## 技术实现

### 架构设计
- 实现了 `HTTPExecutor` 接口，扩展了原有的 `Executor` 接口
- 支持直接修改 `*http.Request` 和 `*http.Response` 对象
- 自动处理Content-Length头部更新
- 保持向后兼容性

### 处理阶段
- `modify_request`: 在 `pre_request` 阶段执行
- `modify_response`: 在 `post_header` 或 `post_body` 阶段执行

### 错误处理
- 参数验证失败时返回详细错误信息
- 修改操作失败时记录错误日志
- 支持部分修改成功的情况

## 注意事项

1. **Content-Length自动更新**: 修改body时会自动更新Content-Length头部
2. **Content-Type处理**: 建议在修改body时同时设置正确的Content-Type
3. **性能考虑**: body修改会涉及内存复制，大文件时需注意性能影响
4. **执行顺序**: 多个动作按配置顺序执行，后面的修改会覆盖前面的修改
5. **参数类型**: 所有参数都是可选的，未指定的参数不会进行修改

## 调试和日志

启用调试日志可以查看详细的修改过程：
```
[INFO] 开始修改HTTP请求: headers=map[...], remove_headers=[...], body_length=...
[DEBUG] 请求头修改完成
[DEBUG] 请求体修改完成，新长度: ...
[INFO] HTTP请求修改完成
```
