package constants

import "time"

// 应用程序常量
const (
	// 应用信息
	AppName    = "FlexProxy"
	AppVersion = "1.0.0"
	UserAgent  = AppName + "/" + AppVersion
)

// 网络相关常量
const (
	// 默认端口
	DefaultHTTPPort  = 8080
	DefaultHTTPSPort = 8443
	DefaultSOCKSPort = 1080
	
	// 超时设置
	DefaultConnectTimeout = 30 * time.Second
	DefaultReadTimeout    = 60 * time.Second
	DefaultWriteTimeout   = 60 * time.Second
	DefaultIdleTimeout    = 120 * time.Second
	
	// 连接池设置
	DefaultMaxIdleConns        = 100
	DefaultMaxIdleConnsPerHost = 10
	DefaultMaxConnsPerHost     = 50
	
	// 重试设置
	DefaultMaxRetries    = 3
	DefaultRetryInterval = 1 * time.Second
	MaxRetryInterval     = 30 * time.Second
	
	// 缓冲区大小
	DefaultBufferSize = 32 * 1024 // 32KB
	MaxBufferSize     = 1024 * 1024 // 1MB
	
	// 防抖延迟
	DefaultDebounceDelay = 100 * time.Millisecond
)

// 代理相关常量
const (
	// 代理类型
	ProxyTypeHTTP  = "http"
	ProxyTypeHTTPS = "https"
	ProxyTypeSOCKS = "socks"
	ProxyTypeSOCKS4 = "socks4"
	ProxyTypeSOCKS5 = "socks5"
	
	// 代理状态
	ProxyStatusActive   = "active"
	ProxyStatusInactive = "inactive"
	ProxyStatusError    = "error"
	ProxyStatusTesting  = "testing"
	
	// 代理选择策略
	StrategyRandom     = "random"
	StrategySequential = "sequential"
	StrategyQuality    = "quality"
	StrategySmart      = "smart"
	StrategyCustom     = "custom"
)

// 负载均衡常量
const (
	// 负载均衡算法
	LoadBalancerRoundRobin         = "round_robin"
	LoadBalancerWeightedRoundRobin = "weighted_round_robin"
	LoadBalancerLeastConnections   = "least_connections"
	LoadBalancerResponseTime       = "response_time"
	LoadBalancerIPHash             = "ip_hash"
	
	// 健康检查
	DefaultHealthCheckInterval = 30 * time.Second
	DefaultHealthCheckTimeout  = 5 * time.Second
	DefaultHealthCheckPath     = "/health"
	MaxConsecutiveFailures     = 3
	MaxConsecutiveSuccesses    = 2
)

// 缓存相关常量
const (
	// 缓存类型
	CacheTypeMemory = "memory"
	CacheTypeRedis  = "redis"
	CacheTypeFile   = "file"
	
	// 缓存默认设置
	DefaultCacheTTL      = 5 * time.Minute
	DefaultCacheTTLSeconds = 300 // 5分钟，单位秒
	DefaultCacheSize     = 1000
	DefaultCleanupInterval = 10 * time.Minute
	
	// 缓存键前缀
	CacheKeyProxyList   = "proxy:list"
	CacheKeyProxyStatus = "proxy:status:"
	CacheKeyUserSession = "user:session:"
	CacheKeyRateLimit   = "rate:limit:"
)

// 配置相关常量
const (
	// 配置文件路径
	DefaultConfigFile = "config.yaml"
	ConfigDirName     = "flexproxy"
	
	// 环境变量前缀
	EnvPrefix = "FLEXPROXY_"
	
	// 配置键
	ConfigKeyLogLevel    = "log.level"
	ConfigKeyLogFormat   = "log.format"
	ConfigKeyServerPort  = "server.port"
	ConfigKeyProxyList   = "proxy.list"
	ConfigKeyStrategy    = "proxy.strategy"
)

// 日志相关常量
const (
	// 日志级别
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
	
	// 日志格式
	LogFormatJSON = "json"
	LogFormatText = "text"
	
	// 时间格式
	TimeFormatDefault = "2006-01-02 15:04:05"
	TimeFormatISO8601 = "2006-01-02T15:04:05Z07:00"
	TimeFormatRFC3339 = time.RFC3339
	
	// 日志文件
	DefaultLogFile     = "flexproxy.log"
	DefaultLogMaxSize  = 100 // MB
	DefaultLogMaxAge   = 30  // 天
	DefaultLogMaxBackups = 10
)

// 注意：错误相关常量已迁移到 common/errors/errors.go 中统一管理

// HTTP 相关常量
const (
	// HTTP 方法
	MethodGET     = "GET"
	MethodPOST    = "POST"
	MethodPUT     = "PUT"
	MethodDELETE  = "DELETE"
	MethodPATCH   = "PATCH"
	MethodHEAD    = "HEAD"
	MethodOPTIONS = "OPTIONS"
	MethodCONNECT = "CONNECT"
	
	// HTTP 状态码
	StatusOK                  = 200
	StatusBadRequest          = 400
	StatusUnauthorized        = 401
	StatusForbidden           = 403
	StatusNotFound            = 404
	StatusMethodNotAllowed    = 405
	StatusTooManyRequests     = 429
	StatusInternalServerError = 500
	StatusBadGateway          = 502
	StatusServiceUnavailable  = 503
	StatusGatewayTimeout      = 504
	
	// HTTP 头
	HeaderContentType     = "Content-Type"
	HeaderContentLength   = "Content-Length"
	HeaderAuthorization   = "Authorization"
	HeaderUserAgent       = "User-Agent"
	HeaderXForwardedFor   = "X-Forwarded-For"
	HeaderXRealIP         = "X-Real-IP"
	HeaderXRequestID      = "X-Request-ID"
	HeaderXTraceID        = "X-Trace-ID"
	
	// Content-Type 值
	ContentTypeJSON = "application/json"
	ContentTypeXML  = "application/xml"
	ContentTypeForm = "application/x-www-form-urlencoded"
	ContentTypeText = "text/plain"
	ContentTypeHTML = "text/html"
)

// 监控和指标常量
const (
	// 指标名称
	MetricRequestTotal     = "requests_total"
	MetricRequestDuration  = "request_duration_seconds"
	MetricProxyStatus      = "proxy_status"
	MetricConnectionsActive = "connections_active"
	MetricErrorsTotal      = "errors_total"
	
	// 指标标签
	LabelMethod    = "method"
	LabelStatus    = "status"
	LabelProxy     = "proxy"
	LabelStrategy  = "strategy"
	LabelErrorType = "error_type"
)

// 安全相关常量
const (
	// 认证类型
	AuthTypeNone   = "none"
	AuthTypeBasic  = "basic"
	AuthTypeBearer = "bearer"
	AuthTypeAPIKey = "apikey"
	
	// 加密算法
	EncryptionAES256 = "aes256"
	EncryptionRSA    = "rsa"
	
	// 默认密钥长度
	DefaultKeyLength = 32
	
	// 令牌过期时间
	DefaultTokenExpiry = 24 * time.Hour
)

// 限流相关常量
const (
	// 限流算法
	RateLimitTokenBucket   = "token_bucket"
	RateLimitLeakyBucket   = "leaky_bucket"
	RateLimitFixedWindow   = "fixed_window"
	RateLimitSlidingWindow = "sliding_window"
	
	// 默认限流设置
	DefaultRateLimit     = 1000 // 每分钟请求数
	DefaultBurstSize     = 100
	DefaultWindowSize    = 1 * time.Minute
	DefaultCleanupPeriod = 5 * time.Minute
)

// 文件和路径常量
const (
	// 文件权限
	FilePermission = 0644
	DirPermission  = 0755
	
	// 文件扩展名
	ExtYAML = ".yaml"
	ExtYML  = ".yml"
	ExtJSON = ".json"
	ExtTOML = ".toml"
	ExtLog  = ".log"
	
	// 目录名称
	DirLogs    = "logs"
	DirConfig  = "config"
	DirData    = "data"
	DirTemp    = "temp"
	DirBackup  = "backup"
)

// 系统相关常量
const (
	// 操作系统
	OSWindows = "windows"
	OSLinux   = "linux"
	OSDarwin  = "darwin"
	
	// 架构
	ArchAMD64 = "amd64"
	ArchARM64 = "arm64"
	Arch386   = "386"
	
	// 信号
	SigTERM = "SIGTERM"
	SigINT  = "SIGINT"
	SigHUP  = "SIGHUP"
	SigUSR1 = "SIGUSR1"
	SigUSR2 = "SIGUSR2"
)

// DNS相关常量
const (
	// DNS服务器
	DefaultDNSServer = "*******:53"
	DefaultPublicDNS = "*******"
	DNSTimeout       = 5 * time.Second
	DefaultDNSTimeout = 5 * time.Second
	DNSRetries       = 3
	
	// DNS模式
	DNSModeLocal  = "local"
	DNSModeRemote = "remote"
	DNSModeCustom = "custom"
)

// 正则表达式常量
const (
	// IP 地址正则
	RegexIPv4 = `^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`
	RegexIPv6 = `^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`
	
	// URL 正则
	RegexURL = `^https?://[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$`
	
	// 端口正则
	RegexPort = `^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$`
	
	// 域名正则
	RegexDomain = `^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$`
)

// 策略相关常量
const (
	// 最小请求数阈值
	MinRequestsForStats = 10
	
	// 响应时间基准（毫秒）
	ResponseTimeBaseline = 5000
	
	// 质量评分相关
	DefaultQualityScore = 0.5
	SuccessRateWeight = 0.7
	ResponseTimeWeight = 0.3
	MaxFailureRate = 0.8
	TopProxyRatio = 0.3
	TopTargetsRatio = 0.3
	
	// 响应时间平滑因子
	ResponseTimeSmoothingFactor = 0.2
	
	// 代理池相关常量
	LatencySmoothingFactor = 0.2
	ErrorRateWeight = 0.6
	LatencyWeight = 0.4
	HighErrorRateThreshold = 0.5
	MediumErrorRateThreshold = 0.3
	LowErrorRateThreshold = 0.1
	
	// 代理管理器相关常量
	ProxyResponseTimeSmoothingFactor = 0.8
	ProxyLatencySmoothingFactor = 0.2
	ProxySuccessRateWeight = 0.7
	ProxyResponseTimeWeight = 0.3
	
	// 动作相关常量
	DefaultRequestTimeoutMS = 5000
	DefaultMaxRedirects = 10
	DefaultActionTimeoutMS = 10000
	
	// 错误恢复相关常量
	DefaultMaxRetryAttempts    = 3
	DefaultInitialRetryDelay   = 100 * time.Millisecond
	DefaultMaxRetryDelay       = 5 * time.Second
	DefaultRetryMultiplier     = 2.0
	DefaultFailureThreshold    = 5
	DefaultSuccessThreshold    = 3
	DefaultCircuitTimeout      = 30 * time.Second
	DefaultCircuitResetTimeout = 60 * time.Second

	// 追踪相关常量
	DefaultHexGeneratorLength = 16
	SequenceModulus          = 1000000
	
	// AWS URL 前缀
	AWSURLPrefix = "aws://"
	
	// 凭证分隔符
	CredentialsSeparator = "@"
	CredentialsDelimiter = ":"
	
	// SOCKS5 协议前缀
	SOCKS5Prefix = "socks"
	
	// 默认监控端口
	DefaultMonitoringPort = 9090
	
	// 默认监控路径
	DefaultMonitoringPath = "/metrics"
)

// 动作类型常量
const (
	// 基础动作类型（8种）
	ActionTypeLog            = "log"
	ActionTypeBanIP          = "banip"
	ActionTypeBanDomain      = "ban_domain"
	ActionTypeBlockRequest   = "block_request"
	ActionTypeModifyRequest  = "modify_request"
	ActionTypeModifyResponse = "modify_response"
	ActionTypeCacheResponse  = "cache_response"
	ActionTypeScript         = "script"

	// 扩展动作类型（8种）
	ActionTypeRetry          = "retry"
	ActionTypeRetrySame      = "retry_same"
	ActionTypeSaveToPool     = "save_to_pool"
	ActionTypeCache          = "cache"
	ActionTypeRequestURL     = "request_url"
	ActionTypeBanIPDomain    = "banipdomain"
	ActionTypeNullResponse   = "null_response"
	ActionTypeBypassProxy    = "bypass_proxy"
)



// 默认配置值
var DefaultConfig = map[string]interface{}{
	"server.host":                "0.0.0.0",
	"server.port":                DefaultHTTPPort,
	"server.read_timeout":        DefaultReadTimeout,
	"server.write_timeout":       DefaultWriteTimeout,
	"server.idle_timeout":        DefaultIdleTimeout,
	"server.max_header_bytes":    1 << 20, // 1MB
	
	"proxy.strategy":             StrategyRandom,
	"proxy.load_balancer":        LoadBalancerRoundRobin,
	"proxy.health_check.enabled": true,
	"proxy.health_check.interval": DefaultHealthCheckInterval,
	"proxy.health_check.timeout":  DefaultHealthCheckTimeout,
	"proxy.health_check.path":     DefaultHealthCheckPath,
	"proxy.max_retries":          DefaultMaxRetries,
	"proxy.retry_interval":       DefaultRetryInterval,
	
	"cache.type":                 CacheTypeMemory,
	"cache.ttl":                  DefaultCacheTTL,
	"cache.size":                 DefaultCacheSize,
	"cache.cleanup_interval":     DefaultCleanupInterval,
	
	"log.level":                  LogLevelInfo,
	"log.format":                 LogFormatJSON,
	"log.file":                   DefaultLogFile,
	"log.max_size":               DefaultLogMaxSize,
	"log.max_age":                DefaultLogMaxAge,
	"log.max_backups":            DefaultLogMaxBackups,
	
	"rate_limit.enabled":         false,
	"rate_limit.algorithm":       RateLimitTokenBucket,
	"rate_limit.rate":            DefaultRateLimit,
	"rate_limit.burst":           DefaultBurstSize,
	"rate_limit.window":          DefaultWindowSize,
	
	"auth.type":                  AuthTypeNone,
	"auth.token_expiry":          DefaultTokenExpiry,
	
	"monitoring.enabled":         false,
	"monitoring.port":            DefaultMonitoringPort,
	"monitoring.path":            DefaultMonitoringPath,
}

// DNS 服务相关常量
const (
	// DNS 缓存设置
	DefaultDNSCacheTTL = 5 * time.Minute
	
	// DNS 清理间隔
	DNSCleanupInterval = 10 * time.Minute
)

// Action 服务相关常量
const (
	// Action 队列设置
	DefaultActionQueueSize = 1000
	DefaultActionWorkers   = 10
	
	// Action 超时设置
	DefaultActionTimeout = 30 * time.Second
	
	// Action 重试设置
	DefaultActionMaxRetries = 3
	DefaultActionRetryDelay = 1 * time.Second
)

// Trigger 服务相关常量
const (
	// Trigger 检查间隔
	DefaultTriggerCheckInterval = 1 * time.Second
	
	// Trigger 运行间隔
	DefaultTriggerRunInterval = 5 * time.Second
)

// 代理服务相关常量
const (
	// 代理轮询间隔
	ProxyRotationInterval = 1
	
	// 代理健康检查
	ProxyHealthCheckInterval = 10 * time.Minute
	
	// 代理连接池大小
	DefaultProxyPoolSize = 100
)

// 注意：错误消息常量已迁移到 common/errors/errors.go 中统一管理

// 状态常量
const (
	// 服务状态
	ServiceStatusStarting = "starting"
	ServiceStatusRunning  = "running"
	ServiceStatusStopping = "stopping"
	ServiceStatusStopped  = "stopped"
	ServiceStatusError    = "error"

	// 任务状态
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"
)

// 检查器相关常量
const (
	// 检查状态
	CheckStatusLive = "LIVE"
	CheckStatusDied = "DIED"

	// 检查器默认设置
	DefaultGoroutines = 50
)

// 证书管理常量
const (
	// 证书目录和文件
	DefaultCertDir     = "./certs"
	CACertFile         = "ca.crt"
	CAKeyFile          = "ca.key"

	// 证书有效期
	CACertValidityYears = 10
	CertValidityDays    = 365

	// 证书权限
	CertFilePermission = 0644
	KeyFilePermission  = 0600

	// 证书组织信息
	CertOrganization = "FlexProxy"
	CertCountry      = "US"
	CertLocality     = "San Francisco"
	CertCommonName   = "FlexProxy Root CA"
)

// 服务器相关常量
const (
	// MIME类型
	MimeTextPlain = "text/plain"

	// 全局代理使用跟踪
	DefaultProxyCooldownMinutes = 5
	DefaultProxyCooldownSeconds = 300

	// 内存和性能阈值
	MemoryThresholdBytes    = 1024 * 1024 * 1024 // 1GB
	GoroutineThresholdCount = 1000
)

// 处理阶段常量
const (
	PreProcess        = "pre"         // 请求前处理
	PostHeaderProcess = "post_header" // 响应头处理
	PostBodyProcess   = "post_body"   // 响应体处理

	// 触发器处理阶段
	PreRequest = "pre"
	PostHeader = "post_header"
	PostBody   = "post_body"
)

// 条件关系常量
const (
	ConditionRelationAND = "AND"
	ConditionRelationOR  = "OR"
	ConditionRelationNOT = "NOT"
)

// AWS 和代理网关常量
const (
	// AWS API Gateway
	AWSAPIGatewayStageName = "mubeng-proxy-gateway"

	// 检查器端点
	DefaultIPInfoEndpoint = "https://ipinfo.io/json"

	// 环境变量
	EnvVarPrefix = "FLEXPROXY_"
)

// 调试和监控常量
const (
	// 调试服务
	DebugServiceName = "debug"

	// 断点相关
	BreakpointPrefix = "bp_"

	// 监控健康检查名称
	HealthCheckMemory     = "memory"
	HealthCheckGoroutines = "goroutines"

	// 性能分析
	ProfileCPU    = "cpu"
	ProfileMemory = "memory"
	ProfileBlock  = "block"
	ProfileMutex  = "mutex"
)

// 文件和目录权限常量
const (
	// 标准权限
	StandardFilePermission = 0644
	StandardDirPermission  = 0755
	SecureFilePermission   = 0600
	SecureDirPermission    = 0700
)

// 网络和协议常量
const (
	// 协议前缀
	HTTPPrefix  = "http://"
	HTTPSPrefix = "https://"

	// 特殊地址
	LocalhostIPv4 = "127.0.0.1"
	LocalhostIPv6 = "::1"
	AllInterfaces = "0.0.0.0"

	// 默认用户代理
	DefaultUserAgent = "FlexProxy/1.0.0"
)

// 协议常量
const (
	// 网络协议
	ProtocolHTTP   = "http"
	ProtocolHTTPS  = "https"
	ProtocolSOCKS4 = "socks4"
	ProtocolSOCKS5 = "socks5"
	ProtocolTCP    = "tcp"
	ProtocolUDP    = "udp"
	
	// DNS 协议
	DNSProtocolUDP   = "udp"
	DNSProtocolTCP   = "tcp"
	DNSProtocolTLS   = "tls"
	DNSProtocolHTTPS = "https"
	DNSProtocolDOH   = "doh"
)

// 操作符常量
const (
	// 比较操作符
	OperatorEquals       = "equals"
	OperatorNotEquals    = "not_equals"
	OperatorContains     = "contains"
	OperatorNotContains  = "not_contains"
	OperatorStartsWith   = "starts_with"
	OperatorEndsWith     = "ends_with"
	OperatorIn           = "in"
	OperatorNotIn        = "not_in"
	OperatorGreaterThan  = "greater_than"
	OperatorLessThan     = "less_than"
	OperatorRegex        = "regex"
)

// 字段名常量
const (
	// HTTP 字段
	FieldURL    = "url"
	FieldMethod = "method"
	FieldHeader = "header"
	FieldBody   = "body"
	FieldIP     = "ip"
	
	// 时间字段
	FieldHour     = "hour"
	FieldWeekday  = "weekday"
	FieldDate     = "date"
	FieldTime     = "time"
	
	// 响应字段
	FieldStatus      = "status"
	FieldContentType = "content_type"
	FieldSize        = "size"
)